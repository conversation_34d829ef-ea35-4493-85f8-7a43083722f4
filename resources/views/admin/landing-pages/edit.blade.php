@extends('layouts.admin')

@section('title', 'Edit Landing Page')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Edit Landing Page</h1>
    <div>
        <a href="{{ route('admin.landing-pages.show', $landingPage) }}" class="btn btn-outline-primary me-2">
            <i class="fas fa-eye me-2"></i>View
        </a>
        <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back
        </a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<form method="POST" action="{{ route('admin.landing-pages.update', $landingPage) }}">
    @csrf
    @method('PUT')

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Page Content</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Title *</label>
                        <input type="text"
                               class="form-control @error('title') is-invalid @enderror"
                               id="title"
                               name="title"
                               value="{{ old('title', $landingPage->title) }}"
                               required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="slug" class="form-label">URL Slug</label>
                        <div class="input-group">
                            <span class="input-group-text">{{ url('/landing/') }}/</span>
                            <input type="text"
                                   class="form-control @error('slug') is-invalid @enderror"
                                   id="slug"
                                   name="slug"
                                   value="{{ old('slug', $landingPage->slug) }}">
                            @error('slug')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <small class="form-text text-muted">
                            Current URL: <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank">{{ route('landing-pages.show', $landingPage) }}</a>
                        </small>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">Content *</label>
                        <textarea class="form-control @error('content') is-invalid @enderror"
                                  id="content"
                                  name="content"
                                  rows="20"
                                  required>{{ old('content', $landingPage->content) }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Use HTML to format your content. This will be displayed on the landing page.</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Page Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input"
                                   type="checkbox"
                                   id="is_active"
                                   name="is_active"
                                   value="1"
                                   {{ old('is_active', $landingPage->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                        <small class="form-text text-muted">Only active pages are visible to visitors</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input"
                                   type="checkbox"
                                   id="has_quiz"
                                   name="has_quiz"
                                   value="1"
                                   {{ old('has_quiz', $landingPage->has_quiz) ? 'checked' : '' }}>
                            <label class="form-check-label" for="has_quiz">
                                Enable Quiz
                            </label>
                        </div>
                        <small class="form-text text-muted">Show quiz button and enable assessment flow</small>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">SEO Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text"
                               class="form-control @error('meta_title') is-invalid @enderror"
                               id="meta_title"
                               name="meta_title"
                               value="{{ old('meta_title', $landingPage->meta_title) }}"
                               maxlength="60">
                        @error('meta_title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Leave blank to use page title</small>
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                  id="meta_description"
                                  name="meta_description"
                                  rows="3"
                                  maxlength="160">{{ old('meta_description', $landingPage->meta_description) }}</textarea>
                        @error('meta_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Recommended: 150-160 characters</small>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-body">
                    <button type="submit" class="btn btn-highlight w-100">
                        <i class="fas fa-save me-2"></i>Update Landing Page
                    </button>
                </div>
            </div>

            @if($landingPage->has_quiz)
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quiz Management</h5>
                    </div>
                    <div class="card-body">
                        <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-info w-100">
                            <i class="fas fa-question-circle me-2"></i>Manage Quiz
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</form>

<!-- Include TinyMCE for rich text editing -->
<script src="https://cdn.tiny.cloud/1/laqmad4amsu52pi0fiou1nb91hscak79b3jsijl5340v0a8s/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize TinyMCE
    tinymce.init({
        selector: '#content',
        height: 500,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
            'bold italic backcolor | alignleft aligncenter ' +
            'alignright alignjustify | bullist numlist outdent indent | ' +
            'removeformat | help',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }'
    });
});
</script>
@endsection
