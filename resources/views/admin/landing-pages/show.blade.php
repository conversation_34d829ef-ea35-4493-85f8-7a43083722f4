@extends('layouts.admin')

@section('title', $landingPage->title)

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">{{ $landingPage->title }}</h1>
    <div>
        <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank" class="btn btn-outline-primary me-2">
            <i class="fas fa-external-link-alt me-2"></i>View Live
        </a>
        <a href="{{ route('admin.landing-pages.edit', $landingPage) }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-edit me-2"></i>Edit
        </a>
        <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back
        </a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Leads</h6>
                        <h3 class="mb-0">{{ $stats['total_leads'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Today</h6>
                        <h3 class="mb-0">{{ $stats['leads_today'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">This Week</h6>
                        <h3 class="mb-0">{{ $stats['leads_this_week'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-week fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Avg Quiz Score</h6>
                        <h3 class="mb-0">{{ $stats['avg_quiz_score'] ? number_format($stats['avg_quiz_score'], 1) : 'N/A' }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Page Details -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Page Details</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Status:</dt>
                            <dd class="col-sm-8">
                                @if($landingPage->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-secondary">Inactive</span>
                                @endif
                            </dd>
                            
                            <dt class="col-sm-4">Quiz:</dt>
                            <dd class="col-sm-8">
                                @if($landingPage->has_quiz)
                                    <span class="badge bg-info">Enabled</span>
                                    <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-sm btn-outline-info ms-2">
                                        <i class="fas fa-cog"></i> Manage
                                    </a>
                                @else
                                    <span class="badge bg-secondary">Disabled</span>
                                @endif
                            </dd>
                            
                            <dt class="col-sm-4">URL:</dt>
                            <dd class="col-sm-8">
                                <code>/landing/{{ $landingPage->slug }}</code>
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Created:</dt>
                            <dd class="col-sm-8">{{ $landingPage->created_at->format('M j, Y g:i A') }}</dd>
                            
                            <dt class="col-sm-4">Updated:</dt>
                            <dd class="col-sm-8">{{ $landingPage->updated_at->format('M j, Y g:i A') }}</dd>
                            
                            <dt class="col-sm-4">Meta Title:</dt>
                            <dd class="col-sm-8">{{ $landingPage->meta_title ?: 'Using page title' }}</dd>
                        </dl>
                    </div>
                </div>
                
                @if($landingPage->meta_description)
                    <div class="mt-3">
                        <strong>Meta Description:</strong>
                        <p class="text-muted mb-0">{{ $landingPage->meta_description }}</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Recent Leads -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent Leads</h5>
                <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                @if($landingPage->leads->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Company</th>
                                    <th>Quiz Score</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($landingPage->leads as $lead)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="rounded-circle bg-primary-soft d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                                    <span class="text-primary fw-bold small">{{ $lead->initials }}</span>
                                                </div>
                                                {{ $lead->name }}
                                            </div>
                                        </td>
                                        <td>{{ $lead->email }}</td>
                                        <td>{{ $lead->company_name }}</td>
                                        <td>
                                            @if($lead->quiz_score !== null)
                                                <span class="badge bg-info">{{ $lead->quiz_score }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $lead->created_at->format('M j, Y') }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.leads.show', $lead) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-user-plus fa-2x text-muted mb-3"></i>
                        <h6 class="text-muted">No leads yet</h6>
                        <p class="text-muted small">Leads will appear here when visitors complete the assessment.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.landing-pages.edit', $landingPage) }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Edit Page
                    </a>
                    @if($landingPage->has_quiz)
                        <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-outline-info">
                            <i class="fas fa-question-circle me-2"></i>Manage Quiz
                        </a>
                    @endif
                    <a href="{{ route('admin.leads.index', ['landing_page_id' => $landingPage->id]) }}" class="btn btn-outline-success">
                        <i class="fas fa-users me-2"></i>View All Leads
                    </a>
                    <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank" class="btn btn-outline-secondary">
                        <i class="fas fa-external-link-alt me-2"></i>Preview Page
                    </a>
                </div>
            </div>
        </div>

        <!-- Quiz Information -->
        @if($landingPage->quiz)
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Quiz Information</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-6">Title:</dt>
                        <dd class="col-sm-6">{{ $landingPage->quiz->title }}</dd>
                        
                        <dt class="col-sm-6">Questions:</dt>
                        <dd class="col-sm-6">{{ $landingPage->quiz->questions->count() }}</dd>
                        
                        <dt class="col-sm-6">Status:</dt>
                        <dd class="col-sm-6">
                            @if($landingPage->quiz->is_active)
                                <span class="badge bg-success">Active</span>
                            @else
                                <span class="badge bg-secondary">Inactive</span>
                            @endif
                        </dd>
                    </dl>
                    
                    @if($landingPage->quiz->description)
                        <div class="mt-3">
                            <strong>Description:</strong>
                            <p class="text-muted small mb-0">{{ $landingPage->quiz->description }}</p>
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
