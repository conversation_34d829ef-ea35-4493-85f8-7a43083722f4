@extends('layouts.admin')

@section('title', 'Leads')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Leads</h1>
    <a href="{{ route('admin.leads.export', request()->query()) }}" class="btn btn-outline-success">
        <i class="fas fa-download me-2"></i>Export CSV
    </a>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.leads.index') }}">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="landing_page_id" class="form-label">Landing Page</label>
                    <select class="form-select" id="landing_page_id" name="landing_page_id">
                        <option value="">All Landing Pages</option>
                        @foreach($landingPages as $landingPage)
                            <option value="{{ $landingPage->id }}" {{ request('landing_page_id') == $landingPage->id ? 'selected' : '' }}>
                                {{ $landingPage->title }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" value="{{ request('search') }}" placeholder="Name, email, or company">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="{{ route('admin.leads.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        @if($leads->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Lead</th>
                            <th>Contact Info</th>
                            <th>Company</th>
                            <th>Landing Page</th>
                            <th>Quiz Score</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($leads as $lead)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle bg-primary-soft d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                            <span class="text-primary fw-bold">{{ $lead->initials }}</span>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $lead->name }}</h6>
                                            @if($lead->availability)
                                                <small class="text-success">
                                                    <i class="fas fa-calendar-check me-1"></i>Availability provided
                                                </small>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="mb-1">
                                            <i class="fas fa-envelope me-1 text-muted"></i>
                                            <a href="mailto:{{ $lead->email }}">{{ $lead->email }}</a>
                                        </div>
                                        @if($lead->phone_number)
                                            <div>
                                                <i class="fas fa-phone me-1 text-muted"></i>
                                                <a href="tel:{{ $lead->phone_number }}">{{ $lead->phone_number }}</a>
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                <td>{{ $lead->company_name }}</td>
                                <td>
                                    <a href="{{ route('admin.landing-pages.show', $lead->landingPage) }}" class="text-decoration-none">
                                        {{ $lead->landingPage->title }}
                                    </a>
                                </td>
                                <td>
                                    @if($lead->quiz_score !== null)
                                        <span class="badge bg-info">{{ $lead->quiz_score }}</span>
                                        @if($lead->pdf_sent)
                                            <br><small class="text-success">
                                                <i class="fas fa-file-pdf me-1"></i>PDF sent
                                            </small>
                                        @endif
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <div>{{ $lead->created_at->format('M j, Y') }}</div>
                                    <small class="text-muted">{{ $lead->created_at->format('g:i A') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.leads.show', $lead) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.leads.edit', $lead) }}" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-danger" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#deleteModal{{ $lead->id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal{{ $lead->id }}" tabindex="-1">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">Delete Lead</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>Are you sure you want to delete the lead for "<strong>{{ $lead->name }}</strong>"?</p>
                                                    <p class="text-danger"><small>This action cannot be undone.</small></p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <form method="POST" action="{{ route('admin.leads.destroy', $lead) }}" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger">Delete</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center">
                {{ $leads->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Leads Found</h5>
                @if(request()->hasAny(['landing_page_id', 'date_from', 'date_to', 'search']))
                    <p class="text-muted">Try adjusting your filters to see more results.</p>
                    <a href="{{ route('admin.leads.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-times me-2"></i>Clear Filters
                    </a>
                @else
                    <p class="text-muted">Leads will appear here when visitors complete landing page assessments.</p>
                    <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-highlight">
                        <i class="fas fa-file-alt me-2"></i>Manage Landing Pages
                    </a>
                @endif
            </div>
        @endif
    </div>
</div>

<!-- Summary Statistics -->
@if($leads->total() > 0)
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Leads</h6>
                            <h3 class="mb-0">{{ $leads->total() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">With Availability</h6>
                            <h3 class="mb-0">{{ $leads->where('availability', '!=', null)->count() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">With Quiz Score</h6>
                            <h3 class="mb-0">{{ $leads->where('quiz_score', '!=', null)->count() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">PDF Reports Sent</h6>
                            <h3 class="mb-0">{{ $leads->where('pdf_sent', true)->count() }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-pdf fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
@endsection
