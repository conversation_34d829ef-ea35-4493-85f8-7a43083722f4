@extends('layouts.admin')

@section('title', 'Lead Details - ' . $lead->name)

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Lead Details</h1>
    <div>
        <a href="{{ route('admin.leads.edit', $lead) }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-edit me-2"></i>Edit
        </a>
        <a href="{{ route('admin.leads.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Leads
        </a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

<div class="row">
    <div class="col-lg-8">
        <!-- Lead Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle bg-primary-soft d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                            <span class="text-primary fw-bold">{{ $lead->initials }}</span>
                        </div>
                        <div>
                            <h5 class="mb-0">{{ $lead->name }}</h5>
                            <small class="text-muted">{{ $lead->company_name }}</small>
                        </div>
                    </div>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Email:</dt>
                            <dd class="col-sm-8">
                                <a href="mailto:{{ $lead->email }}">{{ $lead->email }}</a>
                            </dd>
                            
                            @if($lead->phone_number)
                                <dt class="col-sm-4">Phone:</dt>
                                <dd class="col-sm-8">
                                    <a href="tel:{{ $lead->phone_number }}">{{ $lead->phone_number }}</a>
                                </dd>
                            @endif
                            
                            <dt class="col-sm-4">Company:</dt>
                            <dd class="col-sm-8">{{ $lead->company_name }}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Landing Page:</dt>
                            <dd class="col-sm-8">
                                <a href="{{ route('admin.landing-pages.show', $lead->landingPage) }}">
                                    {{ $lead->landingPage->title }}
                                </a>
                            </dd>
                            
                            <dt class="col-sm-4">Submitted:</dt>
                            <dd class="col-sm-8">{{ $lead->created_at->format('M j, Y g:i A') }}</dd>
                            
                            @if($lead->quiz_score !== null)
                                <dt class="col-sm-4">Quiz Score:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-info fs-6">{{ $lead->quiz_score }}</span>
                                </dd>
                            @endif
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Availability -->
        @if($lead->availability)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Availability
                    </h5>
                </div>
                <div class="card-body">
                    <div class="bg-light p-3 rounded">
                        {!! nl2br(e($lead->availability)) !!}
                    </div>
                </div>
            </div>
        @endif

        <!-- Quiz Responses -->
        @if($lead->quiz_responses && $lead->landingPage->quiz)
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>Quiz Responses
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($lead->landingPage->quiz->questions as $question)
                        @php
                            $response = $lead->quiz_responses[$question->id] ?? null;
                        @endphp
                        
                        <div class="mb-4 pb-3 border-bottom">
                            <h6 class="fw-bold">{{ $question->question }}</h6>
                            
                            @if($response)
                                @if($question->type === 'multiple_choice')
                                    @if(is_array($response))
                                        <ul class="list-unstyled mb-0">
                                            @foreach($response as $selectedOption)
                                                @php
                                                    $options = $question->getFormattedOptions();
                                                    $optionText = $options[$selectedOption]['text'] ?? $selectedOption;
                                                @endphp
                                                <li><i class="fas fa-check text-success me-2"></i>{{ $optionText }}</li>
                                            @endforeach
                                        </ul>
                                    @endif
                                
                                @elseif($question->type === 'single_choice')
                                    @php
                                        $options = $question->getFormattedOptions();
                                        $optionText = $options[$response]['text'] ?? $response;
                                    @endphp
                                    <p class="mb-0"><i class="fas fa-check text-success me-2"></i>{{ $optionText }}</p>
                                
                                @elseif($question->type === 'scale')
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary me-2">{{ $response }}/10</span>
                                        <div class="progress flex-grow-1" style="height: 8px;">
                                            <div class="progress-bar" role="progressbar" style="width: {{ ($response/10)*100 }}%"></div>
                                        </div>
                                    </div>
                                
                                @elseif($question->type === 'text')
                                    <div class="bg-light p-3 rounded">
                                        {!! nl2br(e($response)) !!}
                                    </div>
                                @endif
                            @else
                                <p class="text-muted mb-0"><em>No response provided</em></p>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>

    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="mailto:{{ $lead->email }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-2"></i>Send Email
                    </a>
                    @if($lead->phone_number)
                        <a href="tel:{{ $lead->phone_number }}" class="btn btn-outline-success">
                            <i class="fas fa-phone me-2"></i>Call
                        </a>
                    @endif
                    <a href="{{ route('admin.leads.edit', $lead) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-edit me-2"></i>Edit Lead
                    </a>
                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash me-2"></i>Delete Lead
                    </button>
                </div>
            </div>
        </div>

        <!-- Lead Statistics -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Lead Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    @if($lead->quiz_score !== null)
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <h4 class="text-primary mb-1">{{ $lead->quiz_score }}</h4>
                                <small class="text-muted">Quiz Score</small>
                            </div>
                        </div>
                    @endif
                    
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-info mb-1">{{ $lead->created_at->diffForHumans() }}</h4>
                            <small class="text-muted">Submitted</small>
                        </div>
                    </div>
                    
                    @if($lead->availability)
                        <div class="col-12">
                            <div class="border rounded p-3 bg-success text-white">
                                <i class="fas fa-calendar-check fa-2x mb-2"></i>
                                <div><small>Availability Provided</small></div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- PDF Report Status -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">PDF Report</h5>
            </div>
            <div class="card-body">
                @if($lead->pdf_sent)
                    <div class="text-center text-success">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h6>Report Sent</h6>
                        <p class="small text-muted">{{ $lead->pdf_sent_at ? $lead->pdf_sent_at->format('M j, Y g:i A') : 'Date not recorded' }}</p>
                    </div>
                @else
                    <div class="text-center text-muted">
                        <i class="fas fa-file-pdf fa-3x mb-3"></i>
                        <h6>Report Not Sent</h6>
                        <p class="small">PDF report has not been generated or sent yet.</p>
                        <button class="btn btn-sm btn-outline-primary" disabled>
                            <i class="fas fa-paper-plane me-1"></i>Send Report
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Lead</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the lead for "<strong>{{ $lead->name }}</strong>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ route('admin.leads.destroy', $lead) }}" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
