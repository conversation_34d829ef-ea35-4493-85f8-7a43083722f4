<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $title ?? config('app.name') }} - Admin</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('assets/favicon.ico') }}" type="image/x-icon">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Main Site CSS -->
    <link rel="stylesheet" href="{{ asset('css/styles.css') }}">

    <!-- Custom Admin CSS -->
    <link rel="stylesheet" href="{{ asset('css/admin.css') }}">
</head>
<body class="admin-body">
    <div class="container-fluid">
        <div class="row g-0">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-white shadow-sm sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="d-flex align-items-center justify-content-center mb-4 px-3">
                        <a href="{{ route('admin.dashboard') }}" class="text-decoration-none">
                            <h5 class="mb-0 fw-bold">
                                <span class="logo-endpoint">Endpoint</span><span class="logo-sync">Sync</span>
                                <span class="badge bg-highlight ms-2">Admin</span>
                            </h5>
                        </a>
                    </div>
                    <ul class="nav flex-column px-3">
                        <li class="nav-item mb-2">
                            <a href="{{ route('admin.dashboard') }}" class="nav-link admin-nav-link rounded {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item mb-2">
                            <a href="{{ route('admin.consultations.index') }}" class="nav-link admin-nav-link rounded {{ request()->routeIs('admin.consultations.*') && !request()->routeIs('admin.consultation-statuses.*') ? 'active' : '' }}">
                                <i class="fas fa-comments me-2"></i>
                                Consultations
                            </a>
                        </li>
                        <li class="nav-item mb-2">
                            <a href="{{ route('admin.consultation-statuses.index') }}" class="nav-link admin-nav-link rounded {{ request()->routeIs('admin.consultation-statuses.*') ? 'active' : '' }}">
                                <i class="fas fa-tags me-2"></i>
                                Status Types
                            </a>
                        </li>
                        <li class="nav-item mb-2">
                            <a href="{{ route('admin.landing-pages.index') }}" class="nav-link admin-nav-link rounded {{ request()->routeIs('admin.landing-pages.*') || request()->routeIs('admin.quizzes.*') ? 'active' : '' }}">
                                <i class="fas fa-file-alt me-2"></i>
                                Landing Pages
                            </a>
                        </li>
                        <li class="nav-item mb-2">
                            <a href="{{ route('admin.leads.index') }}" class="nav-link admin-nav-link rounded {{ request()->routeIs('admin.leads.*') ? 'active' : '' }}">
                                <i class="fas fa-user-plus me-2"></i>
                                Leads
                            </a>
                        </li>
                        <li class="nav-item mb-2">
                            <a href="{{ route('admin.users.index') }}" class="nav-link admin-nav-link rounded {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                                <i class="fas fa-users me-2"></i>
                                Users
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a href="{{ route('home') }}" class="nav-link admin-nav-link rounded">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Site
                            </a>
                        </li>
                    </ul>

                    <hr class="my-4 opacity-25">

                    <div class="dropdown px-3">
                        <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" id="dropdownUser" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="rounded-circle bg-blue-soft d-flex align-items-center justify-content-center me-2" style="width: 38px; height: 38px;">
                                <span class="text-primary fw-bold">{{ auth()->user()->initials() }}</span>
                            </div>
                            <div>
                                <strong>{{ auth()->user()->name }}</strong>
                                <div class="small text-muted">Administrator</div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="dropdownUser">
                            <li><a class="dropdown-item" href="{{ route('settings.profile') }}"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i> Sign out
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4 bg-admin-gray-100 min-vh-100">
                <!-- Mobile header -->
                <div class="d-md-none border-bottom pb-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <button class="btn btn-outline-secondary border-0" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar">
                            <i class="fas fa-bars"></i>
                        </button>
                        <a href="{{ route('admin.dashboard') }}" class="text-decoration-none">
                            <h5 class="mb-0 fw-bold">
                                <span class="logo-endpoint">Endpoint</span><span class="logo-sync">Sync</span>
                                <span class="badge bg-highlight ms-2">Admin</span>
                            </h5>
                        </a>
                        <div class="dropdown">
                            <a href="#" class="d-flex align-items-center text-decoration-none" id="dropdownUserMobile" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="rounded-circle bg-blue-soft d-flex align-items-center justify-content-center" style="width: 38px; height: 38px;">
                                    <span class="text-primary fw-bold">{{ auth()->user()->initials() }}</span>
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="dropdownUserMobile">
                                <li><a class="dropdown-item" href="{{ route('settings.profile') }}"><i class="fas fa-user me-2"></i> Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i> Sign out
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Alert messages -->
                @if (session('success'))
                    <div class="alert alert-success d-flex align-items-center mb-4 shadow-sm border-0" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <div>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-danger d-flex align-items-center mb-4 shadow-sm border-0" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <div>
                            {{ session('error') }}
                        </div>
                    </div>
                @endif

                <!-- Page content -->
                <div class="content-wrapper bg-white p-4 rounded shadow-sm mb-4">
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Admin JS -->
    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
